# VS Code 专用工具

这是一个从 AugmentCode-Free 项目中提取的 VS Code 专用工具，提供数据库清理和遥测ID修改功能。

## 功能特性

- **数据库清理**: 清理 VS Code 的 state.vscdb 数据库中包含指定关键字的条目
- **遥测ID修改**: 修改 VS Code 的 storage.json 中的 machineId 和 devDeviceId
- **一键执行**: 运行所有工具的组合操作
- **自动备份**: 操作前自动备份原始文件
- **跨平台支持**: 支持 Windows、macOS 和 Linux

## 系统要求

- Python 3.6+
- VS Code 已安装并至少运行过一次

## 可选依赖

- `colorama`: 用于彩色输出（推荐安装）

```bash
pip install colorama
```

## 使用方法

### 1. 清理数据库

清理 VS Code 数据库中包含指定关键字的条目：

```bash
# 使用默认关键字 "augment"
python vscode_tools.py clean-db

# 使用自定义关键字
python vscode_tools.py clean-db --keyword "your_keyword"
```

### 2. 修改遥测ID

修改 VS Code 的遥测标识符：

```bash
python vscode_tools.py modify-ids
```

### 3. 运行所有工具

执行数据库清理和遥测ID修改的组合操作：

```bash
# 使用默认关键字
python vscode_tools.py run-all

# 使用自定义关键字
python vscode_tools.py run-all --keyword "your_keyword"
```

## 重要提示

⚠️ **使用前请注意**：

1. **关闭 VS Code**: 使用工具前请完全关闭 VS Code
2. **备份数据**: 工具会自动创建备份，但建议手动备份重要数据
3. **管理员权限**: 在某些系统上可能需要管理员权限
4. **测试环境**: 建议先在测试环境中使用

## 文件路径

工具会自动检测以下 VS Code 配置文件路径：

### Windows
- 数据库: `%APPDATA%\Code\User\globalStorage\state.vscdb`
- 存储文件: `%APPDATA%\Code\User\globalStorage\storage.json`

### macOS
- 数据库: `~/Library/Application Support/Code/User/globalStorage/state.vscdb`
- 存储文件: `~/Library/Application Support/Code/User/globalStorage/storage.json`

### Linux
- 数据库: `~/.config/Code/User/globalStorage/state.vscdb`
- 存储文件: `~/.config/Code/User/globalStorage/storage.json`

## 工作原理

### 数据库清理
1. 连接到 VS Code 的 SQLite 数据库 (state.vscdb)
2. 查找 ItemTable 表中包含指定关键字的条目
3. 删除匹配的条目
4. 提交更改

### 遥测ID修改
1. 读取 storage.json 文件
2. 生成新的 64 字符 machineId 和标准 UUID devDeviceId
3. 更新文件中的相关字段
4. 保存修改后的文件

## 错误处理

- 自动备份原始文件
- 操作失败时尝试从备份恢复
- 详细的错误信息和建议
- 彩色输出便于识别不同类型的消息

## 示例输出

```
[INFO] 开始清理 VS Code 数据库 (关键字: 'augment')
[INFO] 正在备份数据库...
[SUCCESS] 备份创建成功: /path/to/state.vscdb.backup
[INFO] 连接到数据库...
[SUCCESS] 成功连接到数据库
[INFO] 找到 3 个包含 'augment' 的条目:
[INFO]   - storage.augment.session.id
[INFO]   - augment.telemetry.enabled
[INFO]   - workspace.augment.settings
[INFO] 正在删除条目...
[SUCCESS] 成功删除 3 个条目
[SUCCESS] 数据库清理完成
```

## 许可证

本工具基于原 AugmentCode-Free 项目，请遵循相应的开源许可证。

## 免责声明

使用本工具的风险由用户自行承担。建议在使用前备份重要数据。作者不对因使用本工具而造成的任何数据丢失或系统问题负责。
