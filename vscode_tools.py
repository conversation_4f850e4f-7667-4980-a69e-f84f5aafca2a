#!/usr/bin/env python3
"""
VS Code 专用工具
提供 VS Code 的数据库清理和遥测ID修改功能

使用方法:
    python vscode_tools.py clean-db --keyword augment
    python vscode_tools.py modify-ids
    python vscode_tools.py run-all --keyword augment
"""

import argparse
import json
import os
import platform
import shutil
import sqlite3
import uuid
from pathlib import Path
from typing import Dict, Union

# 尝试导入 colorama 用于彩色输出
try:
    from colorama import init, Fore, Style
    init(autoreset=True)
    IS_COLORAMA_AVAILABLE = True
except ImportError:
    IS_COLORAMA_AVAILABLE = False


# === 输出函数 ===
def print_message(prefix: str, message: str, color_code: str = "") -> None:
    """带颜色的消息输出"""
    if IS_COLORAMA_AVAILABLE and color_code:
        print(f"{color_code}{prefix}{Style.RESET_ALL} {message}")
    else:
        print(f"{prefix} {message}")


def print_info(message: str) -> None:
    """信息消息"""
    color = Fore.BLUE if IS_COLORAMA_AVAILABLE else ""
    print_message("[INFO]", message, color)


def print_success(message: str) -> None:
    """成功消息"""
    color = Fore.GREEN if IS_COLORAMA_AVAILABLE else ""
    print_message("[SUCCESS]", message, color)


def print_warning(message: str) -> None:
    """警告消息"""
    color = Fore.YELLOW if IS_COLORAMA_AVAILABLE else ""
    print_message("[WARNING]", message, color)


def print_error(message: str) -> None:
    """错误消息"""
    color = Fore.RED if IS_COLORAMA_AVAILABLE else ""
    print_message("[ERROR]", message, color)


# === 工具函数 ===
def create_backup(file_path: Union[str, Path]) -> Union[Path, None]:
    """创建文件备份"""
    original_path = Path(file_path)
    if not original_path.exists():
        print_error(f"备份文件不存在: {original_path}")
        return None

    backup_path = original_path.with_suffix(original_path.suffix + ".backup")
    try:
        shutil.copy2(original_path, backup_path)
        print_success(f"备份创建成功: {backup_path}")
        return backup_path
    except Exception as e:
        print_error(f"创建备份失败 {original_path}: {e}")
        return None


def generate_new_machine_id() -> str:
    """生成新的64字符机器ID"""
    return uuid.uuid4().hex + uuid.uuid4().hex


def generate_new_device_id() -> str:
    """生成新的设备ID"""
    return str(uuid.uuid4())


def get_vscode_paths() -> Dict[str, Path]:
    """获取VS Code配置文件路径"""
    system = platform.system()
    
    if system == "Windows":
        appdata = os.environ.get("APPDATA")
        if not appdata:
            raise RuntimeError("未找到APPDATA环境变量")
        base_dir = Path(appdata) / "Code" / "User"
    elif system == "Darwin":  # macOS
        base_dir = Path.home() / "Library" / "Application Support" / "Code" / "User"
    elif system == "Linux":
        base_dir = Path.home() / ".config" / "Code" / "User"
    else:
        raise RuntimeError(f"不支持的操作系统: {system}")

    return {
        "state_db": base_dir / "globalStorage" / "state.vscdb",
        "storage_json": base_dir / "globalStorage" / "storage.json"
    }


# === 数据库清理功能 ===
def clean_vscode_database(keyword: str = "augment") -> bool:
    """清理VS Code数据库"""
    print_info(f"开始清理 VS Code 数据库 (关键字: '{keyword}')")
    
    try:
        paths = get_vscode_paths()
        db_path = paths["state_db"]
    except RuntimeError as e:
        print_error(str(e))
        return False

    if not db_path.exists():
        print_error(f"数据库文件未找到: {db_path}")
        print_info("请确保 VS Code 已正确安装并至少运行过一次")
        return False

    print_info(f"尝试清理数据库: {db_path}")

    backup_path = None
    try:
        # 创建备份
        print_info("正在备份数据库...")
        backup_path = create_backup(db_path)
        if not backup_path:
            return False

        # 连接数据库
        print_info("连接到数据库...")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        print_success("成功连接到数据库")

        # 查找要删除的条目
        like_pattern = f"%{keyword}%"
        cursor.execute("SELECT key FROM ItemTable WHERE key LIKE ?", (like_pattern,))
        entries_to_delete = cursor.fetchall()
        
        num_entries = len(entries_to_delete)

        if num_entries == 0:
            print_success(f"未找到包含关键字 '{keyword}' 的条目，数据库已经是干净的")
            conn.close()
            return True

        print_info(f"找到 {num_entries} 个包含 '{keyword}' 的条目:")
        for entry in entries_to_delete[:5]:
            print_info(f"  - {entry[0]}")
        if num_entries > 5:
            print_info(f"  ... 还有 {num_entries - 5} 个条目")

        # 删除条目
        print_info("正在删除条目...")
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE ?", (like_pattern,))
        conn.commit()
        
        deleted_rows = cursor.rowcount
        print_success(f"成功删除 {deleted_rows} 个条目")

        conn.close()
        print_success("数据库清理完成")
        return True

    except sqlite3.Error as e:
        print_error(f"SQLite 错误: {e}")
        if backup_path and backup_path.exists():
            try:
                shutil.copy2(backup_path, db_path)
                print_success("数据库已从备份恢复")
            except Exception:
                print_error("从备份恢复失败")
        return False
    except Exception as e:
        print_error(f"意外错误: {e}")
        return False


# === 遥测ID修改功能 ===
def modify_vscode_telemetry_ids() -> bool:
    """修改VS Code遥测ID"""
    print_info("开始修改 VS Code 遥测 ID")
    
    try:
        paths = get_vscode_paths()
        storage_path = paths["storage_json"]
    except RuntimeError as e:
        print_error(str(e))
        return False

    if not storage_path.exists():
        print_error(f"存储文件未找到: {storage_path}")
        return False

    backup_path = create_backup(storage_path)
    if not backup_path:
        return False

    try:
        # 生成新ID
        new_machine_id = generate_new_machine_id()
        new_device_id = generate_new_device_id()
        
        print_info(f"生成新的 machineId: {new_machine_id}")
        print_info(f"生成新的 devDeviceId: {new_device_id}")

        # 读取和修改JSON
        with open(storage_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        modified = False
        
        # 修改根级machineId
        if 'machineId' in data:
            data['machineId'] = new_machine_id
            print_info("更新了根级 machineId")
            modified = True

        # 修改遥测相关ID
        if 'telemetry' not in data:
            data['telemetry'] = {}
        
        data['telemetry']['machineId'] = new_machine_id
        data['telemetry']['devDeviceId'] = new_device_id
        print_info("更新了遥测 ID")
        modified = True

        if modified:
            with open(storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4)
            print_success("成功修改遥测 ID")
            return True
        else:
            print_info("无需修改")
            return True

    except Exception as e:
        print_error(f"修改遥测 ID 失败: {e}")
        if backup_path and backup_path.exists():
            try:
                shutil.copy2(backup_path, storage_path)
                print_success("已从备份恢复")
            except Exception:
                print_error("从备份恢复失败")
        return False


# === 运行所有工具 ===
def run_all_tools(keyword: str = "augment") -> bool:
    """运行所有工具"""
    print_info("开始运行所有 VS Code 工具")
    
    success = True
    
    # 步骤1: 清理数据库
    print_info("步骤 1: 清理数据库")
    if not clean_vscode_database(keyword):
        print_error("数据库清理失败")
        success = False
    
    # 步骤2: 修改遥测ID
    print_info("步骤 2: 修改遥测ID")
    if not modify_vscode_telemetry_ids():
        print_error("遥测ID修改失败")
        success = False
    
    if success:
        print_success("所有工具执行完成")
    else:
        print_warning("部分工具执行失败")
    
    return success


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="VS Code 专用工具")
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # clean-db 命令
    clean_parser = subparsers.add_parser('clean-db', help='清理数据库')
    clean_parser.add_argument('--keyword', default='augment', help='要清理的关键字')
    
    # modify-ids 命令
    subparsers.add_parser('modify-ids', help='修改遥测ID')
    
    # run-all 命令
    run_all_parser = subparsers.add_parser('run-all', help='运行所有工具')
    run_all_parser.add_argument('--keyword', default='augment', help='清理数据库时使用的关键字')
    
    args = parser.parse_args()
    
    if args.command == 'clean-db':
        success = clean_vscode_database(args.keyword)
    elif args.command == 'modify-ids':
        success = modify_vscode_telemetry_ids()
    elif args.command == 'run-all':
        success = run_all_tools(args.keyword)
    else:
        parser.print_help()
        return
    
    if success:
        print_success("操作完成")
    else:
        print_error("操作失败")


if __name__ == "__main__":
    main()
